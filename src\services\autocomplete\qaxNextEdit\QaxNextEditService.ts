import * as vscode from "vscode"
import {
	QaxNextEditConfig,
	QaxNextEditServiceState,
	QaxAnalysisContext,
	QaxAnalysisResult,
	QaxNextEditEvent,
	QaxNextEditEventType,
	QaxNextEditEventCallback,
	QaxJumpSuggestion,
	DEFAULT_QAX_NEXT_EDIT_CONFIG,
} from "./types/QaxNextEditTypes"
import { QaxChangeDetector } from "./services/QaxChangeDetector"
import { QaxLSPService } from "./services/QaxLSPService"
import { QaxASTService } from "./services/QaxASTService"
import { QaxNextEditUIProvider } from "./QaxNextEditUIProvider"
import { createDebouncedFn } from "../utils/createDebouncedFn"

/**
 * QaxNextEdit 主服务类，整合所有组件并提供统一的接口
 */
export class QaxNextEditService {
	private static instance: QaxNextEditService | null = null

	private config: QaxNextEditConfig
	private state: QaxNextEditServiceState
	private changeDetector: QaxChangeDetector
	private lspService: QaxLSPService
	private astService: QaxASTService
	private uiProvider: QaxNextEditUIProvider
	private disposables: vscode.Disposable[] = []
	private eventCallbacks: QaxNextEditEventCallback[] = []
	private debouncedAnalyze: (context: QaxAnalysisContext) => void
	private documentContentCache: Map<string, string> = new Map()

	// 新增的缓存和依赖管理
	private fileDependencies: Map<string, Set<string>>
	private incrementalCache: Map<
		string,
		{
			baseAnalysis: QaxAnalysisResult
			modifications: QaxAnalysisContext[]
			lastModified: Date
		}
	>
	private asyncAnalysisQueue: Set<string>

	private constructor(config: QaxNextEditConfig = DEFAULT_QAX_NEXT_EDIT_CONFIG) {
		this.config = config
		this.state = {
			isEnabled: config.enabled,
			isAnalyzing: false,
			pendingChanges: new Map(),
			cachedResults: new Map(),
		}

		// 初始化服务组件
		this.lspService = QaxLSPService.getInstance()
		this.astService = QaxASTService.getInstance()
		this.changeDetector = new QaxChangeDetector(config)
		this.uiProvider = new QaxNextEditUIProvider()

		// 创建防抖分析函数
		this.debouncedAnalyze = createDebouncedFn(this.performAnalysis.bind(this), config.debounceDelayMs)

		// 初始化缓存和依赖管理
		this.fileDependencies = new Map()
		this.incrementalCache = new Map()
		this.asyncAnalysisQueue = new Set()

		// 设置 UI 事件回调
		this.uiProvider.setEventCallback(this.handleUIEvent.bind(this))

		this.setupEventHandlers()
	}

	public static getInstance(config?: QaxNextEditConfig): QaxNextEditService {
		if (!QaxNextEditService.instance) {
			QaxNextEditService.instance = new QaxNextEditService(config)
		}
		return QaxNextEditService.instance
	}

	public static dispose(): void {
		if (QaxNextEditService.instance) {
			QaxNextEditService.instance.dispose()
			QaxNextEditService.instance = null
		}
	}

	/**
	 * 设置事件处理器
	 */
	private setupEventHandlers(): void {
		// 监听文档变更
		this.disposables.push(
			vscode.workspace.onDidChangeTextDocument((event) => {
				if (this.state.isEnabled && this.shouldAnalyzeDocument(event.document)) {
					this.handleDocumentChange(event)
				}
			}),
		)

		// 监听文档打开
		this.disposables.push(
			vscode.workspace.onDidOpenTextDocument((document) => {
				if (this.state.isEnabled && this.shouldAnalyzeDocument(document)) {
					this.handleDocumentOpened(document)
				}
			}),
		)

		// 监听文档关闭
		this.disposables.push(
			vscode.workspace.onDidCloseTextDocument((document) => {
				this.cleanupDocumentData(document)
			}),
		)

		// 监听活动编辑器变更
		this.disposables.push(
			vscode.window.onDidChangeActiveTextEditor((editor) => {
				if (editor && this.state.isEnabled) {
					this.state.activeFile = editor.document.uri.fsPath
					this.cacheDocumentContent(editor.document)
				}
			}),
		)
	}

	/**
	 * 处理文档变更
	 */
	private handleDocumentChange(event: vscode.TextDocumentChangeEvent): void {
		// 检查服务是否启用
		if (!this.state.isEnabled) {
			return
		}

		// 检查是否应该分析这个文档
		if (!this.shouldAnalyzeDocument(event.document)) {
			return
		}

		// 检查变更是否有意义
		if (!event.contentChanges || event.contentChanges.length === 0) {
			return
		}

		const filePath = event.document.uri.fsPath
		console.log(`🔍 QaxNextEdit: Document changed: ${filePath}`)

		// 过滤掉微小的变更
		const significantChanges = event.contentChanges.filter(change => {
			const hasSignificantText = change.text.length >= 2 || change.rangeLength >= 2
			const isNotWhitespaceOnly = change.text.trim().length > 0 || change.rangeLength > 0
			return hasSignificantText || isNotWhitespaceOnly
		})

		if (significantChanges.length === 0) {
			console.log(`🔍 QaxNextEdit: No significant changes detected, skipping analysis`)
			return
		}

		const beforeContent = this.documentContentCache.get(filePath) || ""

		// 更新缓存
		this.cacheDocumentContent(event.document)

		// 创建分析上下文
		const context: QaxAnalysisContext = {
			filePath,
			document: event.document,
			changes: significantChanges,
			beforeContent,
			afterContent: event.document.getText(),
			languageId: event.document.languageId,
		}

		// 存储待分析的变更
		this.state.pendingChanges.set(filePath, context)

		// 所有分析都使用防抖机制，避免频繁触发
		console.log(`🔍 QaxNextEdit: Scheduling debounced analysis for ${filePath}`)
		this.debouncedAnalyze(context)

		// 异步分析依赖文件
		this.scheduleAsyncDependencyAnalysis(filePath)

		// 发送事件
		this.emitEvent({
			type: QaxNextEditEventType.CHANGE_DETECTED,
			timestamp: new Date(),
			filePath,
			data: {
				changesCount: event.contentChanges.length,
			},
		})
	}

	/**
	 * 处理文档打开
	 */
	private handleDocumentOpened(document: vscode.TextDocument): void {
		const filePath = document.uri.fsPath

		// 缓存文档内容
		this.cacheDocumentContent(document)

		// 检查是否有之前保存的分析结果
		const cachedResult = this.state.cachedResults.get(filePath)
		if (cachedResult && cachedResult.jumpSuggestions.length > 0) {
			console.log(
				`🔄 QaxNextEdit: Restoring ${cachedResult.jumpSuggestions.length} suggestions for reopened file: ${filePath}`,
			)

			// 发送建议恢复事件
			this.emitEvent({
				type: QaxNextEditEventType.SUGGESTIONS_GENERATED,
				timestamp: new Date(),
				filePath,
				data: {
					suggestions: cachedResult.jumpSuggestions,
					count: cachedResult.jumpSuggestions.length,
					restored: true,
				},
			})

			// 显示 UI（如果这是当前活动文件）
			const activeEditor = vscode.window.activeTextEditor
			if (activeEditor && activeEditor.document.uri.fsPath === filePath) {
				this.uiProvider.showSuggestions(cachedResult.jumpSuggestions)
			}
		} else {
			// 如果没有缓存结果，检查是否需要分析依赖
			this.scheduleAsyncDependencyAnalysis(filePath)
		}
	}

	/**
	 * 执行分析
	 */
	private async performAnalysis(context: QaxAnalysisContext): Promise<void> {
		if (!this.state.isEnabled || this.state.isAnalyzing) {
			return
		}

		this.state.isAnalyzing = true
		this.state.lastAnalysisTime = new Date()

		this.emitEvent({
			type: QaxNextEditEventType.ANALYSIS_STARTED,
			timestamp: new Date(),
			filePath: context.filePath,
		})

		try {
			// 执行变更检测和分析
			const result = await this.changeDetector.analyzeChanges(context)

			// 缓存结果
			this.state.cachedResults.set(context.filePath, result)

			// 缓存基础分析结果用于增量分析
			this.incrementalCache.set(context.filePath, {
				baseAnalysis: result,
				modifications: [],
				lastModified: new Date(),
			})

			// 清除待处理的变更
			this.state.pendingChanges.delete(context.filePath)

			// 发送分析完成事件
			this.emitEvent({
				type: QaxNextEditEventType.ANALYSIS_COMPLETED,
				timestamp: new Date(),
				filePath: context.filePath,
				data: result,
			})

			// 如果有建议，发送建议生成事件并显示 UI
			if (result.jumpSuggestions.length > 0) {
				this.emitEvent({
					type: QaxNextEditEventType.SUGGESTIONS_GENERATED,
					timestamp: new Date(),
					filePath: context.filePath,
					data: {
						suggestions: result.jumpSuggestions,
						count: result.jumpSuggestions.length,
					},
				})

				// 显示建议 UI
				await this.uiProvider.showSuggestions(result.jumpSuggestions)
			} else {
				// 清除 UI 如果没有建议
				this.uiProvider.clearUI()
			}
		} catch (error) {
			console.error("QaxNextEditService: Analysis failed:", error)

			this.emitEvent({
				type: QaxNextEditEventType.ERROR_OCCURRED,
				timestamp: new Date(),
				filePath: context.filePath,
				data: {
					error: error instanceof Error ? error.message : "Unknown error",
				},
			})
		} finally {
			this.state.isAnalyzing = false
		}
	}

	/**
	 * 获取文件的分析结果
	 */
	getAnalysisResult(filePath: string): QaxAnalysisResult | null {
		return this.state.cachedResults.get(filePath) || null
	}

	/**
	 * 获取文件的跳转建议
	 */
	getJumpSuggestions(filePath: string): QaxJumpSuggestion[] {
		const result = this.getAnalysisResult(filePath)
		return result?.jumpSuggestions || []
	}

	/**
	 * 处理 UI 事件
	 */
	private handleUIEvent(event: QaxNextEditEvent): void {
		// 转发事件给外部监听器
		this.emitEvent(event)
	}

	/**
	 * 导航到下一个建议
	 */
	navigateToNextSuggestion(): void {
		this.uiProvider.navigateToNextSuggestion()
	}

	/**
	 * 导航到上一个建议
	 */
	navigateToPreviousSuggestion(): void {
		this.uiProvider.navigateToPreviousSuggestion()
	}

	/**
	 * 获取当前建议
	 */
	getCurrentSuggestion(): QaxJumpSuggestion | null {
		return this.uiProvider.getCurrentSuggestion()
	}

	/**
	 * 应用跳转建议
	 */
	async applyJumpSuggestion(suggestion: QaxJumpSuggestion): Promise<boolean> {
		try {
			// 打开文件
			const document = await vscode.workspace.openTextDocument(suggestion.filePath)
			const editor = await vscode.window.showTextDocument(document)

			// 跳转到位置
			editor.selection = new vscode.Selection(suggestion.range.start, suggestion.range.end)
			editor.revealRange(suggestion.range, vscode.TextEditorRevealType.InCenter)

			// 如果有建议的编辑，应用它
			if (suggestion.suggestedEdit) {
				const success = await editor.edit((editBuilder) => {
					editBuilder.replace(suggestion.suggestedEdit!.range, suggestion.suggestedEdit!.newText)
				})

				if (success) {
					this.emitEvent({
						type: QaxNextEditEventType.SUGGESTION_APPLIED,
						timestamp: new Date(),
						filePath: suggestion.filePath,
						data: { suggestionId: suggestion.id },
					})
				}

				return success
			}

			return true
		} catch (error) {
			console.error("QaxNextEditService: Failed to apply jump suggestion:", error)
			return false
		}
	}

	/**
	 * 忽略跳转建议
	 */
	ignoreJumpSuggestion(suggestion: QaxJumpSuggestion): void {
		// 从缓存结果中移除该建议
		const result = this.state.cachedResults.get(suggestion.filePath)
		if (result) {
			result.jumpSuggestions = result.jumpSuggestions.filter((s) => s.id !== suggestion.id)
		}

		this.emitEvent({
			type: QaxNextEditEventType.SUGGESTION_IGNORED,
			timestamp: new Date(),
			filePath: suggestion.filePath,
			data: { suggestionId: suggestion.id },
		})
	}

	/**
	 * 启用/禁用服务
	 */
	setEnabled(enabled: boolean): void {
		this.state.isEnabled = enabled
		this.config.enabled = enabled

		if (!enabled) {
			// 清理所有缓存和待处理的变更
			this.state.pendingChanges.clear()
			this.state.cachedResults.clear()
			// 清理 UI
			this.uiProvider.clearUI()
		}
	}

	/**
	 * 更新配置
	 */
	updateConfig(config: Partial<QaxNextEditConfig>): void {
		this.config = { ...this.config, ...config }
		this.changeDetector.updateConfig(this.config)

		// 重新创建防抖函数
		if (config.debounceDelayMs) {
			this.debouncedAnalyze = createDebouncedFn(this.performAnalysis.bind(this), config.debounceDelayMs)
		}
	}

	/**
	 * 获取服务状态
	 */
	getState(): QaxNextEditServiceState {
		return { ...this.state }
	}

	/**
	 * 获取配置
	 */
	getConfig(): QaxNextEditConfig {
		return { ...this.config }
	}

	/**
	 * 添加事件监听器
	 */
	addEventListener(callback: QaxNextEditEventCallback): void {
		this.eventCallbacks.push(callback)
	}

	/**
	 * 移除事件监听器
	 */
	removeEventListener(callback: QaxNextEditEventCallback): void {
		const index = this.eventCallbacks.indexOf(callback)
		if (index > -1) {
			this.eventCallbacks.splice(index, 1)
		}
	}

	/**
	 * 发送事件
	 */
	private emitEvent(event: QaxNextEditEvent): void {
		for (const callback of this.eventCallbacks) {
			try {
				callback(event)
			} catch (error) {
				console.error("QaxNextEditService: Event callback error:", error)
			}
		}
	}

	/**
	 * 判断是否应该分析文档
	 */
	private shouldAnalyzeDocument(document: vscode.TextDocument): boolean {
		// 过滤掉不需要分析的文档
		if (
			document.uri.scheme.startsWith("git") ||
			document.uri.scheme.startsWith("output") ||
			document.uri.scheme.startsWith("debug") ||
			document.uri.scheme.startsWith("extension-output")
		) {
			console.log(`🔍 QaxNextEdit: Skipping scheme: ${document.uri.scheme}`)
			return false
		}

		// 过滤掉 Untitled 文档（临时文档）
		if (
			document.uri.scheme === "untitled" ||
			document.fileName.startsWith("Untitled-") ||
			document.uri.fsPath.includes("Untitled-")
		) {
			console.log(`🔍 QaxNextEdit: Skipping untitled document: ${document.uri.fsPath || document.fileName}`)
			return false
		}

		// 过滤掉没有文件路径的文档
		if (!document.uri.fsPath || document.uri.fsPath.trim() === "") {
			console.log(`🔍 QaxNextEdit: Skipping document without file path: ${document.uri.toString()}`)
			return false
		}

		// 检查是否是支持的语言
		if (!this.config.supportedLanguages.includes(document.languageId)) {
			console.log(`🔍 QaxNextEdit: Skipping unsupported language: ${document.languageId} for ${document.uri.fsPath}`)
			return false
		}

		// 检查文件是否存在（对于真实文件）
		if (document.uri.scheme === "file") {
			try {
				const fs = require("fs")
				if (!fs.existsSync(document.uri.fsPath)) {
					console.log(`🔍 QaxNextEdit: Skipping non-existent file: ${document.uri.fsPath}`)
					return false
				}
			} catch (error) {
				console.log(`🔍 QaxNextEdit: Error checking file existence: ${document.uri.fsPath}`, error)
				return false
			}
		}

		console.log(`🔍 QaxNextEdit: Document should be analyzed: ${document.uri.fsPath}`)
		return true
	}

	/**
	 * 缓存文档内容
	 */
	private cacheDocumentContent(document: vscode.TextDocument): void {
		this.documentContentCache.set(document.uri.fsPath, document.getText())
	}

	/**
	 * 清理文档数据
	 */
	private cleanupDocumentData(document: vscode.TextDocument): void {
		const filePath = document.uri.fsPath
		this.documentContentCache.delete(filePath)
		this.state.pendingChanges.delete(filePath)
		this.state.cachedResults.delete(filePath)
		this.incrementalCache.delete(filePath)
		this.fileDependencies.delete(filePath)
		this.asyncAnalysisQueue.delete(filePath)
	}

	/**
	 * 检查是否可以进行增量分析
	 */
	private canPerformIncrementalAnalysis(filePath: string, context: QaxAnalysisContext): boolean {
		const cached = this.incrementalCache.get(filePath)
		if (!cached) {
			return false
		}

		// 检查修改时间是否太久
		const timeDiff = Date.now() - cached.lastModified.getTime()
		if (timeDiff > 5 * 60 * 1000) {
			// 5分钟
			return false
		}

		// 检查修改是否过多
		if (cached.modifications.length > 10) {
			return false
		}

		// 检查是否是简单的文本修改
		const hasComplexChanges = context.changes.some((change) => change.rangeLength > 100 || change.text.length > 100)

		return !hasComplexChanges
	}

	/**
	 * 执行增量分析
	 */
	private async performIncrementalAnalysis(filePath: string, context: QaxAnalysisContext): Promise<void> {
		const cached = this.incrementalCache.get(filePath)
		if (!cached) {
			return
		}

		try {
			// 添加新的修改到缓存
			cached.modifications.push(context)
			cached.lastModified = new Date()

			// 基于基础分析和修改历史，更新建议
			const updatedResult = await this.updateAnalysisResult(cached.baseAnalysis, context)

			// 更新缓存结果
			this.state.cachedResults.set(filePath, updatedResult)

			// 发送分析完成事件
			this.emitEvent({
				type: QaxNextEditEventType.ANALYSIS_COMPLETED,
				timestamp: new Date(),
				filePath,
				data: updatedResult,
			})

			console.log(
				`🔄 QaxNextEdit: Incremental analysis completed for ${filePath}, found ${updatedResult.jumpSuggestions.length} suggestions`,
			)
		} catch (error) {
			console.error(`QaxNextEdit: Incremental analysis failed for ${filePath}:`, error)
			// 回退到完整分析
			this.state.pendingChanges.set(filePath, context)
			this.debouncedAnalyze(context)
		}
	}

	/**
	 * 更新分析结果（增量）
	 */
	private async updateAnalysisResult(baseResult: QaxAnalysisResult, context: QaxAnalysisContext): Promise<QaxAnalysisResult> {
		// 简化的增量更新逻辑
		// 在实际实现中，这里应该基于新的变更来更新现有的建议

		// 检测新的变更
		const newChanges = await this.changeDetector.analyzeChanges(context)

		// 合并建议（去重和优化）
		const allSuggestions = [...baseResult.jumpSuggestions, ...newChanges.jumpSuggestions]
		const uniqueSuggestions = this.deduplicateSuggestions(allSuggestions)

		return {
			...newChanges,
			jumpSuggestions: uniqueSuggestions,
			analysisTime: baseResult.analysisTime + newChanges.analysisTime,
			confidence: Math.max(baseResult.confidence, newChanges.confidence),
		}
	}

	/**
	 * 去重建议
	 */
	private deduplicateSuggestions(suggestions: QaxJumpSuggestion[]): QaxJumpSuggestion[] {
		const seen = new Set<string>()
		return suggestions.filter((suggestion) => {
			const key = `${suggestion.filePath}:${suggestion.range.start.line}:${suggestion.range.start.character}:${suggestion.changeType}`
			if (seen.has(key)) {
				return false
			}
			seen.add(key)
			return true
		})
	}

	/**
	 * 安排异步依赖分析
	 */
	private scheduleAsyncDependencyAnalysis(filePath: string): void {
		// 获取文件依赖
		const dependencies = this.getFileDependencies(filePath)

		for (const depPath of dependencies) {
			if (!this.asyncAnalysisQueue.has(depPath) && !this.state.cachedResults.has(depPath)) {
				this.asyncAnalysisQueue.add(depPath)

				// 异步分析依赖文件
				setTimeout(async () => {
					try {
						await this.analyzeFileAsync(depPath)
					} catch (error) {
						console.error(`QaxNextEdit: Async analysis failed for ${depPath}:`, error)
					} finally {
						this.asyncAnalysisQueue.delete(depPath)
					}
				}, 100) // 短延迟以避免阻塞主线程
			}
		}
	}

	/**
	 * 获取文件依赖
	 */
	private getFileDependencies(filePath: string): Set<string> {
		let dependencies = this.fileDependencies.get(filePath)
		if (!dependencies) {
			dependencies = new Set()

			// 简化的依赖检测：查找当前工作区中的相关文件
			const workspaceFiles = vscode.workspace.textDocuments
			for (const doc of workspaceFiles) {
				if (doc.uri.fsPath !== filePath && this.isRelatedFile(filePath, doc.uri.fsPath)) {
					dependencies.add(doc.uri.fsPath)
				}
			}

			this.fileDependencies.set(filePath, dependencies)
		}
		return dependencies
	}

	/**
	 * 检查文件是否相关
	 */
	private isRelatedFile(filePath: string, otherPath: string): boolean {
		// 简化的相关性检测
		const fileExt = filePath.split(".").pop()
		const otherExt = otherPath.split(".").pop()

		// 同类型文件
		if (fileExt === otherExt) {
			return true
		}

		// TypeScript/JavaScript 相关文件
		const jsTypes = ["js", "ts", "jsx", "tsx"]
		if (jsTypes.includes(fileExt || "") && jsTypes.includes(otherExt || "")) {
			return true
		}

		return false
	}

	/**
	 * 异步分析文件
	 */
	private async analyzeFileAsync(filePath: string): Promise<void> {
		try {
			const document = await vscode.workspace.openTextDocument(vscode.Uri.file(filePath))
			const context: QaxAnalysisContext = {
				filePath,
				document,
				changes: [],
				beforeContent: "",
				afterContent: document.getText(),
				languageId: document.languageId,
			}

			const result = await this.changeDetector.analyzeChanges(context)
			this.state.cachedResults.set(filePath, result)

			console.log(`🔍 QaxNextEdit: Async analysis completed for ${filePath}`)
		} catch (error) {
			console.error(`QaxNextEdit: Failed to analyze ${filePath}:`, error)
		}
	}

	/**
	 * 清理资源
	 */
	dispose(): void {
		this.disposables.forEach((d) => d.dispose())
		this.disposables = []
		this.eventCallbacks = []
		this.documentContentCache.clear()
		this.state.pendingChanges.clear()
		this.state.cachedResults.clear()
		this.incrementalCache.clear()
		this.fileDependencies.clear()
		this.asyncAnalysisQueue.clear()

		// 清理 UI 提供者
		this.uiProvider.dispose()

		QaxLSPService.dispose()
		QaxASTService.dispose()
	}
}
